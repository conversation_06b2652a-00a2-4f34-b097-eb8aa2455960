import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'Forever Fest 2026 - <PERSON> & <PERSON>\'s Wedding',
  description: 'Join <PERSON> & <PERSON> as they celebrate their love at Forever Fest 2026! Save the date for an unforgettable wedding celebration.',
  keywords: ['wedding', 'Forever Fest 2026', '<PERSON> and <PERSON>', 'wedding celebration', 'save the date'],
  authors: [{ name: '<PERSON> & <PERSON>' }],
  creator: '<PERSON> & <PERSON>',
  publisher: 'Forever Fest 2026',
  formatDetection: {
    email: false,
    address: false,
    telephone: false
  },
  metadataBase: new URL('https://foreverfest.wedding'),
  alternates: {
    canonical: '/'
  },
  openGraph: {
    title: 'Forever Fest 2026 - <PERSON> & <PERSON>\'s Wedding',
    description: 'Join <PERSON> & <PERSON> as they celebrate their love at Forever Fest 2026! Save the date for an unforgettable wedding celebration.',
    url: 'https://foreverfest.wedding',
    siteName: 'Forever Fest 2026',
    images: [
      {
        url: '/sean_and_eva_banner_photo.png',
        width: 1200,
        height: 630,
        alt: '<PERSON> & <PERSON> - Forever Fest 2026'
      }
    ],
    locale: 'en_US',
    type: 'website'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Forever Fest 2026 - <PERSON> & <PERSON>\'s Wedding',
    description: 'Join <PERSON> & Eva as they celebrate their love at Forever Fest 2026! Save the date for an unforgettable wedding celebration.',
    images: ['/sean_and_eva_banner_photo.png']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      'index': true,
      'follow': true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  },
  icons: {
    icon: '/favicon.png',
    shortcut: '/favicon.png',
    apple: '/favicon.png'
  },
  manifest: '/site.webmanifest'
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
